{"__inputs": [{"name": "DS_PROMETHEUS", "label": "prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "5.0.4"}, {"type": "panel", "id": "graph", "name": "Graph", "version": "5.0.0"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "5.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(requests_total[1s])", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Requests / second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 8}, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "1 - rate(failed_requests_total[1s])/rate(requests_total[1s])", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Requests Success Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "fill": 1, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 15}, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/(.*) - injected/", "color": "#bf1b00"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "running_test", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{name}} - running", "refId": "A"}, {"expr": "failure_injected", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{name}} - injected", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Running Test", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": "1.10", "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "refresh": "5s", "schemaVersion": 16, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Testing", "uid": "-fIxz_kiz", "version": 3}