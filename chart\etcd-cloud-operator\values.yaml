---

image:
  repository: quay.io/quentin_m/etcd-cloud-operator
  pullPolicy: IfNotPresent

replicaCount: 3
service:
  type: ClusterIP

persistence:
  enabled: true
  storageClass: "standard"
  accessModes:
   - ReadWriteOnce
  size: 4Gi
  annotations: {}

networkPolicy:
  enabled: true
  allowExternal: true

setupProbe:
  failureThreshold: 60
  periodSeconds: 10

# ECO configuration, see config.example.yaml for option details
config:
  checkInterval: 15s
  unhealthyMemberTTL: 5m
  etcd:
    dataDir: /var/lib/etcd
    clientTransportSecurity:
      autoTLS: false
      certFile: ""
      keyFile: ""
      trustedCAFile: ""
      clientCertAuth: false
    peerTransportSecurity:
      autoTLS: true
      certFile: ""
      keyFile: ""
      trustedCaFile: ""
      peerClientCertAuth: ""
    backendQuota: "**********"
  snapshot:
    provider: file # This should be configured to S3 in any real environments.
    interval: 30m
    ttl: 24h

resources: {}
nodeSelector: {}
tolerations: []
affinity: {}
