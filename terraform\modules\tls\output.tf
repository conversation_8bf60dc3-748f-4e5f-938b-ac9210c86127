// Copyright 2017 <PERSON> eco authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

output "ca" {
  value = var.enabled == "true" ? local.ca["cert"] : ""
}

output "clients_server_cert" {
  value = var.enabled == "true" ? join("", tls_locally_signed_cert.clients-server.*.cert_pem) : ""
}

output "clients_server_key" {
  value = var.enabled == "true" ? join("", tls_private_key.clients-server.*.private_key_pem) : ""
}

output "clients_cert" {
  value = var.enabled == "true" && var.generate_clients_cert == "true" ? join("", tls_locally_signed_cert.clients.*.cert_pem) : ""
}

output "clients_key" {
  value = var.enabled == "true" && var.generate_clients_cert == "true" ? join("", tls_private_key.clients.*.private_key_pem) : ""
}

output "acl_users_certs" {
  value = var.enabled == "true" && var.generate_clients_cert == "true" ? tls_locally_signed_cert.acl_users : []
}

output "acl_users_keys" {
  value = var.enabled == "true" && var.generate_clients_cert == "true" ? tls_private_key.acl_users : []
}

output "jwt_private_key" {
  value = var.enabled == "true" && var.enable_jwt_token ? join("", tls_private_key.jwt_key.*.private_key_pem) : ""
}

output "jwt_public_key" {
  value = var.enabled == "true" && var.enable_jwt_token ? join("", tls_private_key.jwt_key.*.public_key_pem) : ""
}
