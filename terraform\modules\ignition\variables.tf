// Copyright 2017 <PERSON> eco authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

locals {
  blank_ignition_config = "data:text/plain;charset=utf-8;base64,${base64encode(data.ignition_config.blank.rendered)}"
}

variable "instance_ssh_keys" {
  description = "List of SSH public keys that are allowed to login into nodes"
  type        = list(string)
}

variable "eco_image" {
  description = "Defines the container image to run for ECO"
}

variable "telegraf_image" {
  description = "Defines the container image to run for Telegraf"
}

variable "eco_cert" {
  description = "Defines the certificate to use to encrypt client connections on etcd (optional)"
}

variable "eco_key" {
  description = "Defines the private key to use to encrypt client connections on etcd (optional)"
}

variable "eco_ca" {
  description = "Defines the CA to use to encrypt client connections on etcd (optional)"
}

variable "eco_configuration" {
  description = "Defines the configuration for ECO"
}

variable "telegraf_configuration" {
  description = "Defines the configuration for Telegraf"
}

variable "ignition_extra_config" {
  description = "Extra ignition configuration that will get appended to the default ECO config"
  default     = {}
}

variable "eco_jwt_private_key" {
  description = "Defines the private key to use to sign the jwt auth token (optional)"
}

variable "eco_jwt_public_key" {
  description = "Defines the private key to verify the jwt auth token (optional)"
}

variable "eco_jwt_enabled" {
  description = "Defines whether the jwt auth token is enabled"
  default     = false
}
