---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "etcd-cloud-operator.fullname" . }}-discovery
  labels:
    app.kubernetes.io/name: {{ include "etcd-cloud-operator.name" . }}
    helm.sh/chart: {{ include "etcd-cloud-operator.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  clusterIP: None
  type: ClusterIP
  publishNotReadyAddresses: true
  ports:
    - port: 2378
      targetPort: http
      protocol: TCP
      name: http
    - port: 2379
      targetPort: client
      protocol: TCP
      name: client
    - port: 2380
      targetPort: peer
      protocol: TCP
      name: peer
    - port: 2381
      protocol: TCP
      targetPort: metrics
      name: metrics
  selector:
    app.kubernetes.io/name: {{ include "etcd-cloud-operator.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "etcd-cloud-operator.fullname" . }}-client
  labels:
    app.kubernetes.io/name: {{ include "etcd-cloud-operator.name" . }}
    helm.sh/chart: {{ include "etcd-cloud-operator.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "2381"
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: 2379
      targetPort: client
      protocol: TCP
      name: client
  selector:
    app.kubernetes.io/name: {{ include "etcd-cloud-operator.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
