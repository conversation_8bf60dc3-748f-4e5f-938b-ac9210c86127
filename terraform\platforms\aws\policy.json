{"Version": "2012-10-17", "Statement": [{"Action": "*", "Effect": "Allow", "Resource": ["arn:aws:s3:::${bucket}", "arn:aws:s3:::${bucket}/*"]}, {"Action": "s3:GetObject", "Effect": "Allow", "Resource": ["${config_arn}"]}, {"Action": ["autoscaling:DescribeAutoScalingGroups", "autoscaling:DescribeAutoScalingInstances"], "Effect": "Allow", "Resource": "*"}, {"Action": ["ec2:DescribeInstances"], "Effect": "Allow", "Resource": "*"}, {"Action": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:DescribeKey"], "Effect": "Allow", "Resource": ["${kms_arn}"]}]}