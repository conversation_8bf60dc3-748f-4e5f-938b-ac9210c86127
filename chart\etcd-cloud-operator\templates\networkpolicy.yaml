{{- if .Values.networkPolicy.enabled }}
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: {{ include "etcd-cloud-operator.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "etcd-cloud-operator.name" . }}
    helm.sh/chart: {{ include "etcd-cloud-operator.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: {{ include "etcd-cloud-operator.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  ingress:
    {{- if not .Values.networkPolicy.allowExternal }}
    - ports:
      - port: 2379
    {{- end }}
    # Allow prometheus scrapes, peer discovery and healthchecks
    - ports:
      - port: 2378
      - port: 2379
      - port: 2380
      - port: 2381
      from:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: {{ include "etcd-cloud-operator.name" . }}
              app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}
