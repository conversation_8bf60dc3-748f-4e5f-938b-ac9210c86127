---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "etcd-cloud-operator.fullname" . }}
  labels:
    chart: {{ include "etcd-cloud-operator.fullname" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
    app: {{ include "etcd-cloud-operator.fullname" . }}
data:
  eco.yaml: |
    eco:
      # The interval between each cluster verification by the operator.
      check-interval: {{ .Values.config.checkInterval }}
      # The time after which, an unhealthy member will be removed from the cluster.
      unhealthy-member-ttl: {{ .Values.config.unhealthyMemberTTL }}
      # Configuration of the etcd instance.
      etcd:
        # The address that clients should use to connect to the etcd cluster (i.e.
        # load balancer public address - hostname only, no schema or port number).
        advertise-address: {{ include "etcd-cloud-operator.fullname" . }}-client.{{ .Release.Namespace }}
        # The directory where the etcd data is stored.
        data-dir: {{ .Values.config.etcd.dataDir }}
        # The TLS configuration for clients communication.
        client-transport-security:
          auto-tls: {{ .Values.config.etcd.clientTransportSecurity.autoTLS }}
          cert-file: {{ .Values.config.etcd.clientTransportSecurity.certFile }}
          key-file: {{ .Values.config.etcd.clientTransportSecurity.keyFile }}
          trusted-ca-file: {{ .Values.config.etcd.clientTransportSecurity.trustedCAFile }}
          client-cert-auth: {{ .Values.config.etcd.clientTransportSecurity.clientCertAuth }}
        # The TLS configuration for peers communication.
        peer-transport-security:
          auto-tls: {{ .Values.config.etcd.peerTransportSecurity.autoTLS }}
          cert-file: {{ .Values.config.etcd.peerTransportSecurity.certFile }}
          key-file: {{ .Values.config.etcd.peerTransportSecurity.keyFile }}
          trusted-ca-file: {{ .Values.config.etcd.peerTransportSecurity.trustedCAFile }}
          peer-client-cert-auth: {{ .Values.config.etcd.peerTransportSecurity.peerClientCertAuth }}
        # Defines the maximum amount of data that etcd can store, in bytes, before going into maintenance mode
        backend-quota: {{ .Values.config.etcd.backendQuota }}
      # Configuration of the auto-scaling group provider.
      asg:
        provider: sts
      # Configuration of the snapshot provider.
      snapshot:
        provider: {{ .Values.config.snapshot.provider }}
        # The interval between each snapshot.
        interval: {{ .Values.config.snapshot.interval }}
        # The time after which a backup has to be deleted.
        ttl: {{ .Values.config.snapshot.ttl }}
