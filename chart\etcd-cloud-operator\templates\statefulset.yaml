---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "etcd-cloud-operator.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "etcd-cloud-operator.name" . }}
    helm.sh/chart: {{ include "etcd-cloud-operator.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  serviceName: {{ include "etcd-cloud-operator.fullname"  . }}-discovery
  replicas: {{ default 3 .Values.replicaCount }}
  podManagementPolicy: Parallel
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "etcd-cloud-operator.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "etcd-cloud-operator.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
          - name: data
            mountPath: /var/lib
          - name: config
            mountPath: /etc/eco
            readOnly: true
          env:
          - name: ETCD_API
            value: "3"
          - name: ETCDCTL_INSECURE_SKIP_TLS_VERIFY
            value: "true"
          {{- if .Values.config.etcd.clientTransportSecurity.trustedCaFile }}
          - name: ETCDCTL_CACERT
            value: {{ .Values.config.etcd.clientTransportSecurity.trustedCaFile }}
          {{- end }}
          {{- if .Values.config.etcd.clientTransportSecurity.certFile }}
          - name: ETCDCTL_CERT
            value: {{ .Values.config.etcd.clientTransportSecurity.certFile }}
          {{- end }}
          {{- if .Values.config.etcd.clientTransportSecurity.keyFile }}
          - name: ETCDCTL_KEY
            value: {{ .Values.config.etcd.clientTransportSecurity.keyFile }}
          {{- end }}
          - name: STATEFULSET_SERVICE_NAME
            value: {{ include "etcd-cloud-operator.fullname" . }}-discovery
          - name: STATEFULSET_NAME
            value: {{ include "etcd-cloud-operator.fullname" . }}
          - name: STATEFULSET_REPLICAS
            value: "{{ default 3 .Values.replicaCount }}"
          - name: STATEFULSET_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: STATEFULSET_DNS_CLUSTER_SUFFIX
            value: cluster.local
          ports:
            - name: client
              containerPort: 2379
              protocol: TCP
            - name: http
              containerPort: 2378
              protocol: TCP
            - name: peer
              containerPort: 2380
              protocol: TCP
            - name: metrics
              containerPort: 2381
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: client
            initialDelaySeconds: 60
            periodSeconds: 60
          readinessProbe:
            httpGet:
              path: /status
              port: http
            initialDelaySeconds: 10
            periodSeconds: 30
          startupProbe:
            exec:
              command:
              - /bin/sh
              - -c
              - /usr/local/bin/etcdctl --endpoints=${HOSTNAME}:2379 endpoint health
            failureThreshold: {{ .Values.setupProbe.failureThreshold }}
            periodSeconds: {{ .Values.setupProbe.periodSeconds }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
      - name: config
        configMap:
          name: {{ include "etcd-cloud-operator.fullname" . }}
{{- if .Values.persistence.enabled }}
      - name: data
        persistentVolumeClaim:
          claimName: data
  volumeClaimTemplates:
  - metadata:
      name: data
    {{- with .Values.persistence.annotations }}
    annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value }}
    {{- end }}
    {{- end }}
    spec:
      storageClassName: "{{ .Values.persistence.storageClass }}"
      accessModes:
      {{- range .Values.persistence.accessModes }}
      - {{ . | quote }}
      {{- end }}
      resources:
        requests:
          storage: {{ .Values.persistence.size | quote }}
{{- else }}
      - name: data
        emptyDir: {}
{{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
